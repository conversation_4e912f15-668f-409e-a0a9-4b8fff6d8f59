import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../models/models.dart';
import 'http_service.dart';
import 'notification_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  SharedPreferences? _prefs;
  User? _currentUser;
  String? _currentToken;

  // Initialize the service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadStoredAuth();

    // Initialiser le service de notifications si l'utilisateur est connecté
    if (isAuthenticated) {
      try {
        await NotificationService().initialize();
      } catch (e) {
        debugPrint(
          'AuthService: Erreur lors de l\'initialisation des notifications: $e',
        );
      }
    }
  }

  // Load stored authentication data
  Future<void> _loadStoredAuth() async {
    try {
      final token = _prefs?.getString(AppConstants.tokenKey);
      final userJson = _prefs?.getString(AppConstants.userKey);

      if (token != null && userJson != null) {
        _currentToken = token;
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        _currentUser = User.fromJson(userMap);

        // Verify token is still valid
        if (await _isTokenValid()) {
          debugPrint('Auth: Loaded stored authentication');
        } else {
          await _clearStoredAuth();
        }
      }
    } catch (e) {
      debugPrint('Auth: Error loading stored auth: $e');
      await _clearStoredAuth();
    }
  }

  // Check if token is valid
  Future<bool> _isTokenValid() async {
    if (_currentToken == null) return false;

    try {
      final httpService = HttpService();
      final response = await httpService.get<User>(
        AppConstants.userEndpoint,
        fromJson: (json) => User.fromJson(json),
      );
      return response.success;
    } catch (e) {
      return false;
    }
  }

  // Login user
  Future<LoginResponse> login(String email, String password) async {
    try {
      final httpService = HttpService();
      final loginRequest = LoginRequest(email: email, password: password);

      final response = await httpService.post<LoginResponse>(
        AppConstants.loginEndpoint,
        data: loginRequest.toJson(),
        fromJson: (json) => LoginResponse.fromJson(json),
      );

      if (response.success && response.data != null) {
        final loginResponse = response.data!;
        await _storeAuth(loginResponse.token, loginResponse.user);

        // Initialiser les notifications après la connexion
        try {
          await NotificationService().initialize();
          await _sendFCMTokenToServer();
        } catch (e) {
          debugPrint(
            'Auth: Erreur lors de l\'initialisation des notifications: $e',
          );
        }

        debugPrint('Auth: Login successful for ${loginResponse.user.email}');
        return loginResponse;
      } else {
        throw ApiException(message: response.message);
      }
    } catch (e) {
      debugPrint('Auth: Login failed: $e');
      rethrow;
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      if (_currentToken != null) {
        final httpService = HttpService();
        await httpService.post(AppConstants.logoutEndpoint);
      }
    } catch (e) {
      debugPrint('Auth: Logout API call failed: $e');
      // Continue with local logout even if API call fails
    } finally {
      await _clearStoredAuth();
      debugPrint('Auth: User logged out');
    }
  }

  // Store authentication data
  Future<void> _storeAuth(String token, User user) async {
    _currentToken = token;
    _currentUser = user;

    await _prefs?.setString(AppConstants.tokenKey, token);
    await _prefs?.setString(AppConstants.userKey, jsonEncode(user.toJson()));
  }

  // Clear stored authentication data
  Future<void> _clearStoredAuth() async {
    _currentToken = null;
    _currentUser = null;

    await _prefs?.remove(AppConstants.tokenKey);
    await _prefs?.remove(AppConstants.userKey);
  }

  // Get current user
  User? get currentUser => _currentUser;

  // Get current token
  String? get token => _currentToken;

  // Get token for HTTP service
  Future<String?> getToken() async => _currentToken;

  // Check if user is authenticated
  bool get isAuthenticated => _currentUser != null && _currentToken != null;

  // Check if user is admin
  bool get isAdmin => _currentUser?.isAdmin ?? false;

  // Check if user is employee
  bool get isEmployee => _currentUser?.isEmployee ?? false;

  // Get user role
  String? get userRole => _currentUser?.role;

  // Get user ID
  int? get userId => _currentUser?.id;

  // Get user name
  String? get userName => _currentUser?.name;

  // Get user email
  String? get userEmail => _currentUser?.email;

  // Refresh user data
  Future<User> refreshUser() async {
    try {
      final httpService = HttpService();
      final response = await httpService.get<User>(
        AppConstants.userEndpoint,
        fromJson: (json) => User.fromJson(json),
      );

      if (response.success && response.data != null) {
        _currentUser = response.data!;
        await _prefs?.setString(
          AppConstants.userKey,
          jsonEncode(_currentUser!.toJson()),
        );
        debugPrint('Auth: User data refreshed');
        return _currentUser!;
      } else {
        throw ApiException(message: response.message);
      }
    } catch (e) {
      debugPrint('Auth: Failed to refresh user data: $e');
      rethrow;
    }
  }

  // Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final httpService = HttpService();
      final request = ChangePasswordRequest(
        currentPassword: currentPassword,
        newPassword: newPassword,
        newPasswordConfirmation: confirmPassword,
      );

      final response = await httpService.post(
        '/auth/change-password',
        data: request.toJson(),
      );

      if (!response.success) {
        throw ApiException(message: response.message);
      }

      debugPrint('Auth: Password changed successfully');
    } catch (e) {
      debugPrint('Auth: Failed to change password: $e');
      rethrow;
    }
  }

  // Update user profile
  Future<User> updateProfile({String? name, String? email}) async {
    try {
      final httpService = HttpService();
      final request = UserUpdateRequest(name: name, email: email);

      final response = await httpService.put<User>(
        '/auth/profile',
        data: request.toJson(),
        fromJson: (json) => User.fromJson(json),
      );

      if (response.success && response.data != null) {
        _currentUser = response.data!;
        await _prefs?.setString(
          AppConstants.userKey,
          jsonEncode(_currentUser!.toJson()),
        );
        debugPrint('Auth: Profile updated successfully');
        return _currentUser!;
      } else {
        throw ApiException(message: response.message);
      }
    } catch (e) {
      debugPrint('Auth: Failed to update profile: $e');
      rethrow;
    }
  }

  // Check if token is expired
  bool get isTokenExpired {
    if (_currentToken == null) return true;

    try {
      // Decode base64 token to check expiration
      final parts = _currentToken!.split('|');
      if (parts.length >= 2) {
        final timestamp = int.tryParse(parts[1]);
        if (timestamp != null) {
          final tokenDate = DateTime.fromMillisecondsSinceEpoch(
            timestamp * 1000,
          );
          final expirationDate = tokenDate.add(AppConstants.tokenExpiration);
          return DateTime.now().isAfter(expirationDate);
        }
      }
    } catch (e) {
      debugPrint('Auth: Error checking token expiration: $e');
    }

    return true;
  }

  // Auto-refresh token if needed
  Future<bool> ensureValidToken() async {
    if (!isAuthenticated) return false;

    if (isTokenExpired) {
      debugPrint('Auth: Token expired, logging out');
      await logout();
      return false;
    }

    return true;
  }

  // Listen to authentication state changes
  Stream<bool> get authStateChanges async* {
    bool lastState = isAuthenticated;
    yield lastState;

    while (true) {
      await Future.delayed(const Duration(seconds: 5));
      final currentState = isAuthenticated && !isTokenExpired;
      if (currentState != lastState) {
        lastState = currentState;
        yield currentState;
      }
    }
  }

  // Envoie le token FCM au serveur
  Future<void> _sendFCMTokenToServer() async {
    try {
      final notificationService = NotificationService();
      final fcmToken = notificationService.currentToken;

      if (fcmToken == null) {
        debugPrint('Auth: Aucun token FCM disponible');
        return;
      }

      final httpService = HttpService();
      await httpService.post(
        '/api/user/fcm-token',
        data: {'fcm_token': fcmToken},
      );

      debugPrint('Auth: Token FCM envoyé au serveur avec succès');
    } catch (e) {
      debugPrint('Auth: Erreur lors de l\'envoi du token FCM: $e');
      // Ne pas faire échouer la connexion si l'envoi du token échoue
    }
  }

  // Récupère le token FCM actuel
  Future<String?> getCurrentFCMToken() async {
    try {
      final notificationService = NotificationService();
      return notificationService.currentToken ??
          await notificationService.getSavedToken();
    } catch (e) {
      debugPrint('Auth: Erreur lors de la récupération du token FCM: $e');
      return null;
    }
  }

  // Clear all app data (for logout or reset)
  Future<void> clearAllData() async {
    await _clearStoredAuth();

    // Clear other stored data if needed
    await _prefs?.clear();

    debugPrint('Auth: All app data cleared');
  }
}
