import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/models.dart';
import '../constants/app_constants.dart';
import '../routes/app_routes.dart';

/// Service de gestion des notifications FCM et locales
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // Firebase Messaging
  FirebaseMessaging? _firebaseMessaging;

  // Local Notifications
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // Secure Storage pour les tokens
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  bool _isInitialized = false;
  String? _fcmToken;

  // Callback pour la navigation
  Function(String route, Map<String, dynamic>? arguments)? onNavigate;

  /// Initialise le service de notifications
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialiser Firebase
      await Firebase.initializeApp();
      _firebaseMessaging = FirebaseMessaging.instance;

      // Initialiser les notifications locales
      await _initializeLocalNotifications();

      // Configurer FCM
      await _configureFCM();

      // Récupérer le token FCM
      await _getFCMToken();

      _isInitialized = true;
      debugPrint('NotificationService: Service initialisé avec succès');
    } catch (e) {
      debugPrint('NotificationService: Erreur d\'initialisation: $e');
      rethrow;
    }
  }

  /// Initialise les notifications locales
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Créer le canal de notification pour Android
    if (Platform.isAndroid) {
      await _createNotificationChannel();
    }
  }

  /// Crée le canal de notification Android
  Future<void> _createNotificationChannel() async {
    const androidChannel = AndroidNotificationChannel(
      AppConstants.notificationChannelId,
      AppConstants.notificationChannelName,
      description: AppConstants.notificationChannelDescription,
      importance: Importance.high,
      enableVibration: true,
      playSound: true,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(androidChannel);
  }

  /// Configure Firebase Cloud Messaging
  Future<void> _configureFCM() async {
    if (_firebaseMessaging == null) return;

    // Demander les permissions
    await _requestNotificationPermissions();

    // Configurer les handlers pour les différents états
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Gérer les notifications quand l'app est terminée
    final initialMessage = await _firebaseMessaging!.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }

    // Écouter les changements de token
    _firebaseMessaging!.onTokenRefresh.listen(_onTokenRefresh);
  }

  /// Demande les permissions de notification
  Future<void> _requestNotificationPermissions() async {
    if (_firebaseMessaging == null) return;

    final settings = await _firebaseMessaging!.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    debugPrint(
      'NotificationService: Permission status: ${settings.authorizationStatus}',
    );
  }

  /// Récupère et sauvegarde le token FCM
  Future<void> _getFCMToken() async {
    if (_firebaseMessaging == null) return;

    try {
      final token = await _firebaseMessaging!.getToken();
      if (token != null) {
        _fcmToken = token;
        await _saveTokenSecurely(token);
        debugPrint('NotificationService: FCM Token obtenu et sauvegardé');
      }
    } catch (e) {
      debugPrint(
        'NotificationService: Erreur lors de la récupération du token: $e',
      );
    }
  }

  /// Sauvegarde le token de manière sécurisée
  Future<void> _saveTokenSecurely(String token) async {
    try {
      await _secureStorage.write(key: 'fcm_token', value: token);
      debugPrint('NotificationService: Token sauvegardé de manière sécurisée');
    } catch (e) {
      debugPrint(
        'NotificationService: Erreur lors de la sauvegarde du token: $e',
      );
    }
  }

  /// Récupère le token sauvegardé
  Future<String?> getSavedToken() async {
    try {
      return await _secureStorage.read(key: 'fcm_token');
    } catch (e) {
      debugPrint('NotificationService: Erreur lors de la lecture du token: $e');
      return null;
    }
  }

  /// Gère les messages en premier plan
  void _handleForegroundMessage(RemoteMessage message) {
    debugPrint(
      'NotificationService: Message reçu en premier plan: ${message.messageId}',
    );

    // Afficher une notification locale
    _showLocalNotification(message);
  }

  /// Gère le tap sur une notification
  void _handleNotificationTap(RemoteMessage message) {
    debugPrint('NotificationService: Notification tapée: ${message.messageId}');

    final data = message.data;
    if (data.containsKey('site_id')) {
      // Naviguer vers l'écran de détails du site
      onNavigate?.call(AppRoutes.siteNotificationDetails, {
        'siteId': data['site_id'],
        'notificationData': data,
      });
    }
  }

  /// Callback pour le tap sur notification locale
  void _onNotificationTapped(NotificationResponse response) {
    debugPrint(
      'NotificationService: Notification locale tapée: ${response.id}',
    );

    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        if (data['site_id'] != null) {
          onNavigate?.call(AppRoutes.siteNotificationDetails, {
            'siteId': data['site_id'],
            'notificationData': data,
          });
        }
      } catch (e) {
        debugPrint(
          'NotificationService: Erreur lors du parsing du payload: $e',
        );
      }
    }
  }

  /// Gère le rafraîchissement du token
  void _onTokenRefresh(String token) {
    debugPrint('NotificationService: Token FCM rafraîchi');
    _fcmToken = token;
    _saveTokenSecurely(token);
  }

  /// Affiche une notification locale
  Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    if (notification == null) return;

    const androidDetails = AndroidNotificationDetails(
      AppConstants.notificationChannelId,
      AppConstants.notificationChannelName,
      channelDescription: AppConstants.notificationChannelDescription,
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
      icon: '@mipmap/ic_launcher',
      styleInformation: BigTextStyleInformation(''),
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      notification.title,
      notification.body,
      details,
      payload: jsonEncode(message.data),
    );
  }

  /// Affiche une notification personnalisée
  Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        AppConstants.notificationChannelId,
        AppConstants.notificationChannelName,
        channelDescription: AppConstants.notificationChannelDescription,
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
        styleInformation: BigTextStyleInformation(''),
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        details,
        payload: payload,
      );

      // Feedback haptique
      if (!kIsWeb) {
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      debugPrint('NotificationService: Erreur lors de l\'affichage: $e');
    }
  }

  /// Affiche une notification d'attribution de site
  Future<void> showSiteAssignmentNotification({
    required String siteName,
    required String siteAddress,
    required String assignedBy,
    required String siteId,
    String? assignmentTime,
  }) async {
    final title = 'تم تعيينك لموقع جديد';
    final body = 'تم تعيينك للعمل في $siteName\nالعنوان: $siteAddress';

    final payload = jsonEncode({
      'type': 'site_assignment',
      'site_id': siteId,
      'site_name': siteName,
      'site_address': siteAddress,
      'assigned_by': assignedBy,
      'assignment_time': assignmentTime ?? DateTime.now().toIso8601String(),
    });

    await showNotification(title: title, body: body, payload: payload);
  }

  /// Affiche une notification avec action
  Future<void> showNotificationWithAction({
    required String title,
    required String body,
    String? actionLabel,
    VoidCallback? onAction,
  }) async {
    await showNotification(title: title, body: body);

    // Pour l'instant, juste logger l'action
    if (actionLabel != null && onAction != null) {
      debugPrint('Action disponible: $actionLabel');
    }
  }

  /// Annule une notification spécifique
  Future<void> cancelNotification(int id) async {
    try {
      await _localNotifications.cancel(id);
      debugPrint('NotificationService: Notification $id annulée');
    } catch (e) {
      debugPrint('NotificationService: Erreur lors de l\'annulation: $e');
    }
  }

  /// Annule toutes les notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _localNotifications.cancelAll();
      debugPrint('NotificationService: Toutes les notifications annulées');
    } catch (e) {
      debugPrint('NotificationService: Erreur lors de l\'annulation: $e');
    }
  }

  /// Récupère le token FCM actuel
  String? get currentToken => _fcmToken;

  /// Vérifie si le service est initialisé
  bool get isInitialized => _isInitialized;

  /// Nettoie les ressources
  Future<void> dispose() async {
    try {
      // Nettoyer les ressources si nécessaire
      debugPrint('NotificationService: Ressources nettoyées');
    } catch (e) {
      debugPrint('NotificationService: Erreur lors du nettoyage: $e');
    }
  }
}
