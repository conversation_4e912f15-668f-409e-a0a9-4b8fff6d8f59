import 'package:flutter/foundation.dart';
import '../services/monitoring_service.dart';
import '../services/api_service.dart';

/// Provider pour la gestion de la surveillance des employés
class MonitoringProvider extends ChangeNotifier {
  final MonitoringService _monitoringService = MonitoringService();
  final ApiService _apiService = ApiService();

  // État de chargement
  bool _isLoading = false;
  String? _errorMessage;

  // Données de surveillance
  List<EmployeeMonitoringStatus> _employeeStatuses = [];
  List<SiteMonitoringStatus> _siteStatuses = [];
  MonitoringStats? _stats;

  // Configuration
  bool _autoRefreshEnabled = true;
  Duration _refreshInterval = const Duration(minutes: 2);

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  List<EmployeeMonitoringStatus> get employeeStatuses => _employeeStatuses;
  List<SiteMonitoringStatus> get siteStatuses => _siteStatuses;
  MonitoringStats? get stats => _stats;
  bool get autoRefreshEnabled => _autoRefreshEnabled;
  Duration get refreshInterval => _refreshInterval;
  bool get isMonitoring => _monitoringService.isMonitoring;

  /// Initialise la surveillance
  Future<void> initialize() async {
    await loadMonitoringData();

    // Écouter les changements du service de surveillance
    _monitoringService.addListener(_onMonitoringServiceChanged);
  }

  /// Charge les données de surveillance
  Future<void> loadMonitoringData() async {
    _setLoading(true);
    _clearError();

    try {
      // Charger les statuts des employés
      await _loadEmployeeStatuses();

      // Charger les statuts des sites
      await _loadSiteStatuses();

      // Charger les statistiques
      await _loadStats();
    } catch (e) {
      _setError('فشل في تحميل بيانات المراقبة: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Charge les statuts des employés
  Future<void> _loadEmployeeStatuses() async {
    try {
      final employeeData = await _apiService.getEmployeeMonitoringStatus();

      _employeeStatuses = employeeData.map((data) {
        return EmployeeMonitoringStatus(
          userId: data['user_id'] ?? 0,
          userName: data['user_name'] ?? 'غير محدد',
          isOnline: data['is_online'] ?? false,
          isOnSite: data['is_on_site'] ?? false,
          isMonitored: data['is_monitored'] ?? false,
          currentSiteId: data['current_site_id'],
          currentSiteName: data['current_site_name'],
          lastSeen: data['last_seen'] != null
              ? DateTime.parse(data['last_seen'])
              : DateTime.now(),
          batteryLevel: data['battery_level'],
          gpsAccuracy: data['gps_accuracy']?.toDouble(),
        );
      }).toList();
    } catch (e) {
      debugPrint('Erreur lors du chargement des statuts des employés: $e');
      // En cas d'erreur, utiliser des données de test
      _employeeStatuses = [
        EmployeeMonitoringStatus(
          userId: 1,
          userName: 'أحمد محمد',
          isOnline: true,
          isOnSite: true,
          currentSiteId: 1,
          currentSiteName: 'موقع البناء الرئيسي',
          lastSeen: DateTime.now().subtract(const Duration(minutes: 5)),
          batteryLevel: 85,
          gpsAccuracy: 12.5,
        ),
        EmployeeMonitoringStatus(
          userId: 2,
          userName: 'فاطمة علي',
          isOnline: true,
          isOnSite: false,
          currentSiteId: null,
          currentSiteName: null,
          lastSeen: DateTime.now().subtract(const Duration(minutes: 15)),
          batteryLevel: 45,
          gpsAccuracy: 8.2,
        ),
      ];
    }
  }

  /// Charge les statuts des sites
  Future<void> _loadSiteStatuses() async {
    try {
      final siteData = await _apiService.getSiteMonitoringStatus();

      _siteStatuses = siteData.map((data) {
        return SiteMonitoringStatus(
          siteId: data['site_id'] ?? 0,
          siteName: data['site_name'] ?? 'غير محدد',
          totalAssignedEmployees: data['total_assigned_employees'] ?? 0,
          presentEmployees: data['present_employees'] ?? 0,
          absentEmployees: data['absent_employees'] ?? 0,
          lastActivity: data['last_activity'] != null
              ? DateTime.parse(data['last_activity'])
              : DateTime.now(),
          isActive: data['is_active'] ?? false,
        );
      }).toList();
    } catch (e) {
      debugPrint('Erreur lors du chargement des statuts des sites: $e');
      // En cas d'erreur, utiliser des données de test
      _siteStatuses = [
        SiteMonitoringStatus(
          siteId: 1,
          siteName: 'موقع البناء الرئيسي',
          totalAssignedEmployees: 5,
          presentEmployees: 3,
          absentEmployees: 2,
          lastActivity: DateTime.now().subtract(const Duration(minutes: 2)),
          isActive: true,
        ),
        SiteMonitoringStatus(
          siteId: 2,
          siteName: 'موقع التطوير الجديد',
          totalAssignedEmployees: 3,
          presentEmployees: 1,
          absentEmployees: 2,
          lastActivity: DateTime.now().subtract(const Duration(hours: 1)),
          isActive: true,
        ),
      ];
    }
  }

  /// Charge les statistiques de surveillance
  Future<void> _loadStats() async {
    try {
      final totalEmployees = _employeeStatuses.length;
      final onlineEmployees = _employeeStatuses.where((e) => e.isOnline).length;
      final onSiteEmployees = _employeeStatuses.where((e) => e.isOnSite).length;
      final activeSites = _siteStatuses.where((s) => s.isActive).length;

      _stats = MonitoringStats(
        totalEmployees: totalEmployees,
        onlineEmployees: onlineEmployees,
        onSiteEmployees: onSiteEmployees,
        offlineEmployees: totalEmployees - onlineEmployees,
        activeSites: activeSites,
        totalSites: _siteStatuses.length,
        averageResponseTime: const Duration(seconds: 45),
        lastUpdateTime: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Erreur lors du chargement des statistiques: $e');
      rethrow;
    }
  }

  /// Démarre la surveillance pour un employé
  Future<void> startEmployeeMonitoring(int userId) async {
    try {
      await _monitoringService.startEmployeeMonitoring(userId);

      // Mettre à jour le statut de l'employé
      final index = _employeeStatuses.indexWhere((e) => e.userId == userId);
      if (index != -1) {
        _employeeStatuses[index] = _employeeStatuses[index].copyWith(
          isMonitored: true,
        );
        notifyListeners();
      }
    } catch (e) {
      _setError('فشل في بدء مراقبة الموظف: ${e.toString()}');
      rethrow;
    }
  }

  /// Arrête la surveillance pour un employé
  Future<void> stopEmployeeMonitoring(int userId) async {
    try {
      await _monitoringService.stopEmployeeMonitoring(userId);

      // Mettre à jour le statut de l'employé
      final index = _employeeStatuses.indexWhere((e) => e.userId == userId);
      if (index != -1) {
        _employeeStatuses[index] = _employeeStatuses[index].copyWith(
          isMonitored: false,
        );
        notifyListeners();
      }
    } catch (e) {
      _setError('فشل في إيقاف مراقبة الموظف: ${e.toString()}');
      rethrow;
    }
  }

  /// Vérifie un employé sur un site
  Future<void> checkEmployeeOnSite(
    int userId,
    double latitude,
    double longitude,
  ) async {
    try {
      await _monitoringService.checkEmployeeOnSite(userId, latitude, longitude);

      // Recharger les données
      await loadMonitoringData();
    } catch (e) {
      _setError('فشل في التحقق من الموظف: ${e.toString()}');
      rethrow;
    }
  }

  /// Active/désactive le rafraîchissement automatique
  void toggleAutoRefresh() {
    _autoRefreshEnabled = !_autoRefreshEnabled;
    notifyListeners();
  }

  /// Configure l'intervalle de rafraîchissement
  void setRefreshInterval(Duration interval) {
    _refreshInterval = interval;
    notifyListeners();
  }

  /// Callback lors des changements du service de surveillance
  void _onMonitoringServiceChanged() {
    notifyListeners();
  }

  /// Définit l'état de chargement
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Définit un message d'erreur
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// Efface le message d'erreur
  void _clearError() {
    _errorMessage = null;
  }

  /// Nettoie les ressources
  @override
  void dispose() {
    _monitoringService.removeListener(_onMonitoringServiceChanged);
    _monitoringService.dispose();
    super.dispose();
  }
}

/// Statut de surveillance d'un employé
class EmployeeMonitoringStatus {
  final int userId;
  final String userName;
  final bool isOnline;
  final bool isOnSite;
  final bool isMonitored;
  final int? currentSiteId;
  final String? currentSiteName;
  final DateTime lastSeen;
  final int? batteryLevel;
  final double? gpsAccuracy;

  const EmployeeMonitoringStatus({
    required this.userId,
    required this.userName,
    required this.isOnline,
    required this.isOnSite,
    this.isMonitored = false,
    this.currentSiteId,
    this.currentSiteName,
    required this.lastSeen,
    this.batteryLevel,
    this.gpsAccuracy,
  });

  EmployeeMonitoringStatus copyWith({
    int? userId,
    String? userName,
    bool? isOnline,
    bool? isOnSite,
    bool? isMonitored,
    int? currentSiteId,
    String? currentSiteName,
    DateTime? lastSeen,
    int? batteryLevel,
    double? gpsAccuracy,
  }) {
    return EmployeeMonitoringStatus(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      isOnline: isOnline ?? this.isOnline,
      isOnSite: isOnSite ?? this.isOnSite,
      isMonitored: isMonitored ?? this.isMonitored,
      currentSiteId: currentSiteId ?? this.currentSiteId,
      currentSiteName: currentSiteName ?? this.currentSiteName,
      lastSeen: lastSeen ?? this.lastSeen,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      gpsAccuracy: gpsAccuracy ?? this.gpsAccuracy,
    );
  }
}

/// Statut de surveillance d'un site
class SiteMonitoringStatus {
  final int siteId;
  final String siteName;
  final int totalAssignedEmployees;
  final int presentEmployees;
  final int absentEmployees;
  final DateTime lastActivity;
  final bool isActive;

  const SiteMonitoringStatus({
    required this.siteId,
    required this.siteName,
    required this.totalAssignedEmployees,
    required this.presentEmployees,
    required this.absentEmployees,
    required this.lastActivity,
    required this.isActive,
  });

  double get presenceRate => totalAssignedEmployees > 0
      ? (presentEmployees / totalAssignedEmployees) * 100
      : 0.0;
}

/// Statistiques de surveillance
class MonitoringStats {
  final int totalEmployees;
  final int onlineEmployees;
  final int onSiteEmployees;
  final int offlineEmployees;
  final int activeSites;
  final int totalSites;
  final Duration averageResponseTime;
  final DateTime lastUpdateTime;

  const MonitoringStats({
    required this.totalEmployees,
    required this.onlineEmployees,
    required this.onSiteEmployees,
    required this.offlineEmployees,
    required this.activeSites,
    required this.totalSites,
    required this.averageResponseTime,
    required this.lastUpdateTime,
  });

  double get onlineRate =>
      totalEmployees > 0 ? (onlineEmployees / totalEmployees) * 100 : 0.0;

  double get onSiteRate =>
      totalEmployees > 0 ? (onSiteEmployees / totalEmployees) * 100 : 0.0;
}
