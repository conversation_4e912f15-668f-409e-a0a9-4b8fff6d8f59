import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/providers.dart';
import '../../models/models.dart';
import '../../widgets/common/custom_error_widget.dart';

class SiteAssignmentScreen extends StatefulWidget {
  final Site site;

  const SiteAssignmentScreen({super.key, required this.site});

  @override
  State<SiteAssignmentScreen> createState() => _SiteAssignmentScreenState();
}

class _SiteAssignmentScreenState extends State<SiteAssignmentScreen> {
  final Set<int> _selectedEmployeeIds = <int>{};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
    _loadCurrentAssignments();
  }

  void _loadData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<EmployeesProvider>().refreshEmployees();
    });
  }

  void _loadCurrentAssignments() {
    // Charger les assignations actuelles du site
    final currentAssignments =
        widget.site.users?.map((user) => user.id).toSet() ?? <int>{};
    setState(() {
      _selectedEmployeeIds.addAll(currentAssignments);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تعيين الموظفين - ${widget.site.name}'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isLoading ? null : _saveAssignments,
            tooltip: 'حفظ التعيينات',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSiteInfo(),
          _buildSelectedCount(),
          Expanded(child: _buildEmployeesList()),
        ],
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildSiteInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.surfaceWhite,
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlueLight,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.location_on,
              color: AppColors.primaryBlue,
              size: 24,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.site.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'الإحداثيات: ${widget.site.coordinates}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedCount() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Icon(Icons.people, color: AppColors.primaryBlue, size: 20),
          const SizedBox(width: 8),
          Text(
            'الموظفون المحددون: ${_selectedEmployeeIds.length}',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: AppColors.primaryBlue,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          if (_selectedEmployeeIds.isNotEmpty)
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedEmployeeIds.clear();
                });
              },
              child: const Text('إلغاء تحديد الكل'),
            ),
        ],
      ),
    );
  }

  Widget _buildEmployeesList() {
    return Consumer<EmployeesProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.state == EmployeesState.error) {
          return CustomErrorWidget(
            message: provider.errorMessage ?? 'حدث خطأ في تحميل البيانات',
            onRetry: _loadData,
          );
        }

        final employees = provider.employees
            .where((emp) => emp.role == 'employee')
            .toList();

        if (employees.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.people_outline,
                  size: 100,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(height: 20),
                Text(
                  'لا يوجد موظفون',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          itemCount: employees.length,
          itemBuilder: (context, index) {
            final employee = employees[index];
            return _buildEmployeeCard(employee);
          },
        );
      },
    );
  }

  Widget _buildEmployeeCard(User employee) {
    final isSelected = _selectedEmployeeIds.contains(employee.id);

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: CheckboxListTile(
        value: isSelected,
        onChanged: (bool? value) {
          setState(() {
            if (value == true) {
              _selectedEmployeeIds.add(employee.id);
            } else {
              _selectedEmployeeIds.remove(employee.id);
            }
          });
        },
        title: Text(
          employee.name,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              employee.email,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: AppColors.primaryBlueLight,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'موظف',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.primaryBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        secondary: CircleAvatar(
          backgroundColor: isSelected
              ? AppColors.primaryBlue
              : AppColors.backgroundGrey,
          child: Icon(
            Icons.person,
            color: isSelected ? AppColors.textWhite : AppColors.textSecondary,
          ),
        ),
        activeColor: AppColors.primaryBlue,
        checkColor: AppColors.textWhite,
        controlAffinity: ListTileControlAffinity.trailing,
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: AppColors.surfaceWhite,
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveAssignments,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: AppColors.textWhite,
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.textWhite,
                        ),
                      ),
                    )
                  : const Text('حفظ التعيينات'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveAssignments() async {
    if (_selectedEmployeeIds.isEmpty) {
      _showMessage('يرجى تحديد موظف واحد على الأقل');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await context.read<SitesProvider>().assignSiteToUsers(
        widget.site.id,
        _selectedEmployeeIds.toList(),
      );

      if (success && mounted) {
        _showMessage('تم حفظ التعيينات بنجاح', isSuccess: true);
        Navigator.pop(context, true);
      } else if (mounted) {
        final errorMsg =
            context.read<SitesProvider>().errorMessage ??
            'فشل في حفظ التعيينات';
        _showMessage(errorMsg);
      }
    } catch (e) {
      if (mounted) {
        _showMessage('حدث خطأ أثناء حفظ التعيينات');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showMessage(String message, {bool isSuccess = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isSuccess ? AppColors.success : AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
