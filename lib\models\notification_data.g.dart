// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationData _$NotificationDataFromJson(Map<String, dynamic> json) =>
    NotificationData(
      id: json['id'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
      data: json['data'] as Map<String, dynamic>?,
      createdAt: json['created_at'] as String?,
      readAt: json['read_at'] as String?,
      isRead: json['isRead'] as bool? ?? false,
    );

Map<String, dynamic> _$NotificationDataToJson(NotificationData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'body': instance.body,
      'type': _$NotificationTypeEnumMap[instance.type]!,
      'data': instance.data,
      'created_at': instance.createdAt,
      'read_at': instance.readAt,
      'isRead': instance.isRead,
    };

const _$NotificationTypeEnumMap = {
  NotificationType.siteAssignment: 'site_assignment',
  NotificationType.general: 'general',
  NotificationType.reminder: 'reminder',
};

SiteAssignmentNotification _$SiteAssignmentNotificationFromJson(
        Map<String, dynamic> json) =>
    SiteAssignmentNotification(
      siteId: (json['site_id'] as num).toInt(),
      siteName: json['site_name'] as String,
      siteAddress: json['site_address'] as String?,
      assignedAt: json['assigned_at'] as String,
      assignedBy: json['assigned_by'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$SiteAssignmentNotificationToJson(
        SiteAssignmentNotification instance) =>
    <String, dynamic>{
      'site_id': instance.siteId,
      'site_name': instance.siteName,
      'site_address': instance.siteAddress,
      'assigned_at': instance.assignedAt,
      'assigned_by': instance.assignedBy,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
    };

FCMNotificationRequest _$FCMNotificationRequestFromJson(
        Map<String, dynamic> json) =>
    FCMNotificationRequest(
      userIds: (json['user_ids'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      title: json['title'] as String,
      body: json['body'] as String,
      type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
      data: json['data'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$FCMNotificationRequestToJson(
        FCMNotificationRequest instance) =>
    <String, dynamic>{
      'user_ids': instance.userIds,
      'title': instance.title,
      'body': instance.body,
      'type': _$NotificationTypeEnumMap[instance.type]!,
      'data': instance.data,
    };

NotificationResponse _$NotificationResponseFromJson(
        Map<String, dynamic> json) =>
    NotificationResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      messageAr: json['message_ar'] as String?,
      sentCount: (json['sent_count'] as num?)?.toInt(),
      failedCount: (json['failed_count'] as num?)?.toInt(),
      failedTokens: (json['failed_tokens'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$NotificationResponseToJson(
        NotificationResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'message_ar': instance.messageAr,
      'sent_count': instance.sentCount,
      'failed_count': instance.failedCount,
      'failed_tokens': instance.failedTokens,
    };

FCMTokenData _$FCMTokenDataFromJson(Map<String, dynamic> json) => FCMTokenData(
      userId: (json['user_id'] as num).toInt(),
      token: json['token'] as String,
      deviceId: json['device_id'] as String?,
      platform: json['platform'] as String,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$FCMTokenDataToJson(FCMTokenData instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'token': instance.token,
      'device_id': instance.deviceId,
      'platform': instance.platform,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
