<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Traits\ApiResponseTrait;
use App\Models\User;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Contrôleur pour la gestion des notifications FCM
 * Gère l'envoi de notifications push aux employés
 */
class NotificationController extends Controller
{
    use ApiResponseTrait;

    /**
     * Sauvegarde le token FCM d'un utilisateur
     */
    public function saveFCMToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fcm_token' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Invalid FCM token data.',
                'بيانات رمز FCM غير صحيحة.',
                422,
                $validator->errors()
            );
        }

        try {
            $user = auth()->user();
            
            // Mettre à jour le token FCM de l'utilisateur
            $user->update([
                'fcm_token' => $request->fcm_token,
                'fcm_token_updated_at' => Carbon::now(),
            ]);

            Log::info('FCM token saved for user', [
                'user_id' => $user->id,
                'token_length' => strlen($request->fcm_token)
            ]);

            return $this->successResponse(
                null,
                'FCM token saved successfully.',
                'تم حفظ رمز FCM بنجاح.'
            );

        } catch (\Exception $e) {
            Log::error('Error saving FCM token', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse(
                'Failed to save FCM token.',
                'فشل في حفظ رمز FCM.',
                500
            );
        }
    }

    /**
     * Envoie une notification d'attribution de site
     */
    public function sendSiteAssignmentNotification(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'integer|exists:users,id',
            'site_id' => 'required|integer|exists:sites,id',
            'assigned_by' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Invalid notification data.',
                'بيانات الإشعار غير صحيحة.',
                422,
                $validator->errors()
            );
        }

        try {
            $site = Site::findOrFail($request->site_id);
            $assignedBy = $request->assigned_by ?? auth()->user()->name;
            $userIds = $request->user_ids;

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($userIds as $userId) {
                $user = User::find($userId);
                
                if (!$user || !$user->fcm_token) {
                    $results[] = [
                        'user_id' => $userId,
                        'status' => 'failed',
                        'reason' => 'No FCM token found'
                    ];
                    $failureCount++;
                    continue;
                }

                $notificationResult = $this->sendFCMNotification(
                    $user->fcm_token,
                    'تم تعيينك لموقع جديد',
                    "تم تعيينك للعمل في {$site->name}",
                    [
                        'type' => 'site_assignment',
                        'site_id' => (string)$site->id,
                        'site_name' => $site->name,
                        'site_address' => "خط العرض: {$site->latitude}, خط الطول: {$site->longitude}",
                        'assigned_by' => $assignedBy,
                        'assignment_time' => Carbon::now()->toIso8601String(),
                    ]
                );

                if ($notificationResult['success']) {
                    $successCount++;
                    $results[] = [
                        'user_id' => $userId,
                        'status' => 'sent',
                        'message_id' => $notificationResult['message_id'] ?? null
                    ];

                    // Enregistrer la notification dans la base de données
                    $this->logNotification($userId, $site->id, 'site_assignment', $assignedBy);
                } else {
                    $failureCount++;
                    $results[] = [
                        'user_id' => $userId,
                        'status' => 'failed',
                        'reason' => $notificationResult['error'] ?? 'Unknown error'
                    ];
                }
            }

            Log::info('Site assignment notifications sent', [
                'site_id' => $site->id,
                'total_users' => count($userIds),
                'success_count' => $successCount,
                'failure_count' => $failureCount
            ]);

            return $this->successResponse([
                'total_sent' => $successCount,
                'total_failed' => $failureCount,
                'details' => $results
            ], 'Notifications sent.', 'تم إرسال الإشعارات.');

        } catch (\Exception $e) {
            Log::error('Error sending site assignment notifications', [
                'error' => $e->getMessage(),
                'site_id' => $request->site_id ?? null
            ]);

            return $this->errorResponse(
                'Failed to send notifications.',
                'فشل في إرسال الإشعارات.',
                500
            );
        }
    }

    /**
     * Envoie une notification FCM via l'API HTTP v1 de Firebase
     */
    private function sendFCMNotification($fcmToken, $title, $body, $data = [])
    {
        try {
            // Récupérer le token d'accès OAuth2 pour Firebase
            $accessToken = $this->getFirebaseAccessToken();
            
            if (!$accessToken) {
                return ['success' => false, 'error' => 'Failed to get Firebase access token'];
            }

            $projectId = env('FIREBASE_PROJECT_ID');
            $url = "https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send";

            $payload = [
                'message' => [
                    'token' => $fcmToken,
                    'notification' => [
                        'title' => $title,
                        'body' => $body,
                    ],
                    'data' => $data,
                    'android' => [
                        'notification' => [
                            'channel_id' => 'clockin_notifications',
                            'priority' => 'high',
                            'default_sound' => true,
                            'default_vibrate_timings' => true,
                        ]
                    ],
                    'apns' => [
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $title,
                                    'body' => $body,
                                ],
                                'sound' => 'default',
                                'badge' => 1,
                            ]
                        ]
                    ]
                ]
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ])->post($url, $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    'success' => true,
                    'message_id' => $responseData['name'] ?? null
                ];
            } else {
                Log::error('FCM API error', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                
                return [
                    'success' => false,
                    'error' => 'FCM API error: ' . $response->status()
                ];
            }

        } catch (\Exception $e) {
            Log::error('Error sending FCM notification', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Récupère le token d'accès OAuth2 pour Firebase
     * Note: Vous devez configurer les credentials de service Firebase
     */
    private function getFirebaseAccessToken()
    {
        try {
            // Chemin vers le fichier de credentials de service Firebase
            $credentialsPath = storage_path('app/firebase-service-account.json');
            
            if (!file_exists($credentialsPath)) {
                Log::error('Firebase service account file not found', [
                    'path' => $credentialsPath
                ]);
                return null;
            }

            // Utiliser la bibliothèque Google Auth pour obtenir le token
            // Vous devez installer: composer require google/auth
            $credentials = json_decode(file_get_contents($credentialsPath), true);
            
            // Pour simplifier, retourner un token factice
            // En production, utilisez Google\Auth\Credentials\ServiceAccountCredentials
            return env('FIREBASE_SERVER_KEY'); // Fallback vers l'ancienne méthode

        } catch (\Exception $e) {
            Log::error('Error getting Firebase access token', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Enregistre la notification dans la base de données
     */
    private function logNotification($userId, $siteId, $type, $sentBy)
    {
        try {
            DB::table('notifications')->insert([
                'user_id' => $userId,
                'site_id' => $siteId,
                'type' => $type,
                'title' => 'تم تعيينك لموقع جديد',
                'body' => 'تم تعيينك للعمل في موقع جديد',
                'sent_by' => $sentBy,
                'sent_at' => Carbon::now(),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        } catch (\Exception $e) {
            Log::error('Error logging notification', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Récupère l'historique des notifications d'un utilisateur
     */
    public function getUserNotifications(Request $request)
    {
        try {
            $user = auth()->user();
            $perPage = $request->get('per_page', 20);

            $notifications = DB::table('notifications')
                ->where('user_id', $user->id)
                ->orderBy('sent_at', 'desc')
                ->paginate($perPage);

            return $this->successResponse(
                $notifications,
                'Notifications retrieved successfully.',
                'تم استرداد الإشعارات بنجاح.'
            );

        } catch (\Exception $e) {
            Log::error('Error retrieving user notifications', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse(
                'Failed to retrieve notifications.',
                'فشل في استرداد الإشعارات.',
                500
            );
        }
    }
}
