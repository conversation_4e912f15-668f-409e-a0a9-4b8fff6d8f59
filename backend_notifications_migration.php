<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration pour créer les tables liées aux notifications FCM
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ajouter les colonnes FCM à la table users
        Schema::table('users', function (Blueprint $table) {
            $table->text('fcm_token')->nullable()->after('remember_token');
            $table->timestamp('fcm_token_updated_at')->nullable()->after('fcm_token');
        });

        // Créer la table notifications
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('site_id')->nullable()->constrained()->onDelete('set null');
            $table->string('type', 50); // 'site_assignment', 'reminder', etc.
            $table->string('title');
            $table->text('body');
            $table->json('data')->nullable(); // Données supplémentaires
            $table->string('sent_by')->nullable(); // Qui a envoyé la notification
            $table->timestamp('sent_at');
            $table->timestamp('read_at')->nullable();
            $table->boolean('is_read')->default(false);
            $table->string('fcm_message_id')->nullable(); // ID du message FCM
            $table->enum('status', ['sent', 'failed', 'delivered'])->default('sent');
            $table->text('error_message')->nullable(); // Message d'erreur si échec
            $table->timestamps();

            // Index pour améliorer les performances
            $table->index(['user_id', 'sent_at']);
            $table->index(['user_id', 'is_read']);
            $table->index(['type', 'sent_at']);
        });

        // Créer la table notification_templates (optionnel)
        Schema::create('notification_templates', function (Blueprint $table) {
            $table->id();
            $table->string('type', 50)->unique(); // Type de notification
            $table->string('title_template'); // Template du titre avec placeholders
            $table->text('body_template'); // Template du corps avec placeholders
            $table->json('default_data')->nullable(); // Données par défaut
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Insérer les templates par défaut
        DB::table('notification_templates')->insert([
            [
                'type' => 'site_assignment',
                'title_template' => 'تم تعيينك لموقع جديد',
                'body_template' => 'تم تعيينك للعمل في {{site_name}}. العنوان: {{site_address}}',
                'default_data' => json_encode([
                    'action' => 'view_site_details',
                    'priority' => 'high'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'type' => 'attendance_reminder',
                'title_template' => 'تذكير بالحضور',
                'body_template' => 'لا تنس تسجيل حضورك في {{site_name}}',
                'default_data' => json_encode([
                    'action' => 'open_attendance',
                    'priority' => 'medium'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'type' => 'shift_change',
                'title_template' => 'تغيير في الوردية',
                'body_template' => 'تم تغيير وردية العمل في {{site_name}}. الوقت الجديد: {{new_time}}',
                'default_data' => json_encode([
                    'action' => 'view_schedule',
                    'priority' => 'high'
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_templates');
        Schema::dropIfExists('notifications');
        
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['fcm_token', 'fcm_token_updated_at']);
        });
    }
};
