// LoadingWidget Usage Examples for ClockIn App
// This file demonstrates how to use the different LoadingWidget variants

import 'package:flutter/material.dart';
import 'lib/widgets/common/loading_widget.dart';
import 'lib/constants/app_colors.dart';

class LoadingWidgetExamples extends StatelessWidget {
  const LoadingWidgetExamples({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('LoadingWidget Examples'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.textWhite,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Example 1: Default LoadingWidget
            _buildExampleSection(
              title: '1. Default LoadingWidget',
              description: 'Standard loading widget with message',
              child: const SizedBox(
                height: 200,
                child: LoadingWidget(
                  message: 'جاري تحميل البيانات...',
                ),
              ),
            ),

            // Example 2: LoadingWidget without message
            _buildExampleSection(
              title: '2. LoadingWidget without message',
              description: 'Loading widget without text message',
              child: const SizedBox(
                height: 150,
                child: LoadingWidget(
                  showMessage: false,
                ),
              ),
            ),

            // Example 3: Custom color LoadingWidget
            _buildExampleSection(
              title: '3. Custom color LoadingWidget',
              description: 'Loading widget with custom color',
              child: const SizedBox(
                height: 200,
                child: LoadingWidget(
                  message: 'جاري المعالجة...',
                  color: AppColors.success,
                ),
              ),
            ),

            // Example 4: LoadingWidgetMini
            _buildExampleSection(
              title: '4. LoadingWidgetMini',
              description: 'Minimal loading widget for inline use',
              child: const SizedBox(
                height: 100,
                child: LoadingWidgetMini(),
              ),
            ),

            // Example 5: LoadingWidgetCustom
            _buildExampleSection(
              title: '5. LoadingWidgetCustom',
              description: 'Custom loading widget with background',
              child: const SizedBox(
                height: 150,
                child: LoadingWidgetCustom(
                  message: 'جاري الحفظ...',
                  backgroundColor: AppColors.backgroundLight,
                ),
              ),
            ),

            // Example 6: LoadingWidgetShimmer
            _buildExampleSection(
              title: '6. LoadingWidgetShimmer',
              description: 'Shimmer effect for list items',
              child: Column(
                children: [
                  const LoadingWidgetShimmer(height: 20),
                  const SizedBox(height: 8),
                  const LoadingWidgetShimmer(height: 16, width: 200),
                  const SizedBox(height: 8),
                  LoadingWidgetShimmer(
                    height: 40,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ],
              ),
            ),

            // Example 7: In a real screen context
            _buildExampleSection(
              title: '7. Real usage in screen',
              description: 'How it would appear in a real screen',
              child: Container(
                height: 300,
                decoration: BoxDecoration(
                  color: AppColors.backgroundWhite,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.borderLight),
                ),
                child: const LoadingWidget(
                  message: 'جاري تحميل تفاصيل الموقع...',
                  padding: EdgeInsets.all(32),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExampleSection({
    required String title,
    required String description,
    required Widget child,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.backgroundWhite,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.borderLight),
            ),
            child: child,
          ),
        ],
      ),
    );
  }
}

/*
Usage in your screens:

1. Basic loading state:
```dart
if (isLoading) {
  return const LoadingWidget(
    message: 'جاري التحميل...',
  );
}
```

2. Inline loading:
```dart
child: isLoading 
  ? const LoadingWidgetMini()
  : YourContentWidget(),
```

3. Custom loading with background:
```dart
LoadingWidgetCustom(
  message: 'جاري حفظ البيانات...',
  backgroundColor: AppColors.backgroundLight,
)
```

4. Shimmer for list loading:
```dart
ListView.builder(
  itemCount: isLoading ? 5 : items.length,
  itemBuilder: (context, index) {
    if (isLoading) {
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: LoadingWidgetShimmer(height: 60),
      );
    }
    return YourListItem(items[index]);
  },
)
```

5. In the site notification details screen:
```dart
Widget _buildBody() {
  if (_isLoading) {
    return const LoadingWidget(
      message: 'جاري تحميل تفاصيل الموقع...',
    );
  }
  // ... rest of your content
}
```
*/
